using FluentValidation.TestHelper;
using ReviewYourTipster.Application.AccountVerification.VerifyAccountVerificationCode;

namespace ReviewYourTipster.ApplicationTests.AccountVerification.VerifyAccountVerificationCode;

public class VerifyAccountVerificationCodeCommandValidatorTests
{
    private readonly VerifyAccountVerificationCodeCommandValidator _validator = new();

    [Fact]
    public void Validate_ReturnsRequiredError_WhenCodeIsEmpty()
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = string.Empty };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorMessage("Code is required")
            .WithErrorCode("Code.Required");
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenCodeIsNull()
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = null! };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorCode("Code.Required");
    }

    [Fact]
    public void Validate_ReturnsRequiredError_WhenCodeIsWhitespace()
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = "   " };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorMessage("Code is required")
            .WithErrorCode("Code.Required");
    }

    [Theory]
    [InlineData("12345")] // 5 digits - too short
    [InlineData("1234")] // 4 digits - too short
    [InlineData("123")] // 3 digits - too short
    [InlineData("12")] // 2 digits - too short
    [InlineData("1")] // 1 digit - too short
    public void Validate_ReturnsInvalidLengthError_WhenCodeIsTooShort(string code)
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = code };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorMessage("Code must be 6 digits")
            .WithErrorCode("Code.InvalidLength");
    }

    [Theory]
    [InlineData("1234567")] // 7 digits - too long
    [InlineData("********")] // 8 digits - too long
    [InlineData("********9")] // 9 digits - too long
    [InlineData("**********")] // 10 digits - too long
    public void Validate_ReturnsInvalidLengthError_WhenCodeIsTooLong(string code)
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = code };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorMessage("Code must be 6 digits")
            .WithErrorCode("Code.InvalidLength");
    }

    [Theory]
    [InlineData("123456")] // All numeric
    [InlineData("ABCDEF")] // All uppercase letters
    [InlineData("abcdef")] // All lowercase letters
    [InlineData("ABC123")] // Mixed letters and numbers
    [InlineData("123ABC")] // Mixed numbers and letters
    [InlineData("A1B2C3")] // Alternating letters and numbers
    [InlineData("!@#$%^")] // Special characters
    [InlineData("12AB!@")] // Mixed alphanumeric and special
    public void Validate_PassesValidation_WhenCodeIsExactly6Characters(string code)
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = code };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_PassesValidation_WhenCodeIsValid6DigitNumber()
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = "987654" };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldNotHaveValidationErrorFor(x => x.Code);
    }

    [Fact]
    public void Validate_ReturnsMultipleErrors_WhenCodeIsEmptyString()
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = string.Empty };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorCode("Code.Required");
        
        // Note: When code is empty, the Length validation might not trigger
        // as NotEmpty takes precedence, but we should verify the behavior
    }

    [Theory]
    [InlineData("      ")] // 6 spaces - meets length but fails NotEmpty
    [InlineData("\t\t\t\t\t\t")] // 6 tabs - meets length but fails NotEmpty
    [InlineData("\n\n\n\n\n\n")] // 6 newlines - meets length but fails NotEmpty
    public void Validate_ReturnsRequiredError_WhenCodeIs6WhitespaceCharacters(string code)
    {
        // Arrange
        var command = new VerifyAccountVerificationCodeCommand { Code = code };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Code)
            .WithErrorMessage("Code is required")
            .WithErrorCode("Code.Required");
    }
}
