using ReviewYourTipster.Application.Abstractions.Authentication;
using ReviewYourTipster.Application.Abstractions.Common;
using ReviewYourTipster.Application.Abstractions.Data;
using ReviewYourTipster.Application.Abstractions.Data.Repositories;
using ReviewYourTipster.Application.Abstractions.Messaging;
using ReviewYourTipster.Domain.Users;
using ReviewYourTipster.SharedKernel;

namespace ReviewYourTipster.Application.AccountVerification.VerifyAccountVerificationCode;

/// <summary>
///     Handles verification of account verification codes
/// </summary>
public class
    VerifyAccountVerificationCodeCommandHandler(
        IUserRepository userRepository,
        ICodeProvider codeProvider,
        IUnitOfWork unitOfWork,
        IUserContext userContext)
    : ICommandHandler<VerifyAccountVerificationCodeCommand, bool>
{
    public async Task<Result<bool>> Handle(VerifyAccountVerificationCodeCommand request,
        CancellationToken cancellationToken)
    {
        // Get the current user
        var loggedInUserId = userContext.UserId;
        var user = await userRepository.GetUserByIdAsync(loggedInUserId, cancellationToken);

        if (user is null)
            return Result.Failure<bool>(UserErrors.UserNotFound);

        if (user.IsVerified)
            return Result.Failure<bool>(UserErrors.UserAlreadyVerified);

        // Verify the code
        var (isValid, _, _) = codeProvider.VerifyCode(
            request.Code,
            user.Id.ToString(),
            VerificationCodePurposes.AccountVerification);

        if (!isValid)
            return Result.Failure<bool>(UserErrors.InvalidAccountVerificationCode);

        // Invalidate the code to prevent reuse
        codeProvider.InvalidateCode(request.Code);

        try
        {
            // Update user verification status
            user.UpdateVerificationStatus(true);

            // Save changes
            await unitOfWork.CommitAsync(cancellationToken);

            return Result.Success(true);
        }
        catch (Exception ex)
        {
            return Result.Failure<bool>(Error.Problem(
                "User.VerificationFailed",
                $"Failed to verify user account: {ex.Message}"));
        }
    }
}